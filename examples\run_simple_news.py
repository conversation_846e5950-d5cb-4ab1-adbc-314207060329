"""
简单运行新闻策略的脚本
用户可以设置爬取间隔，策略只打印最新新闻
"""
import asyncio
import logging
import sys
from datetime import datetime

# 设置日志格式
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    datefmt='%H:%M:%S'
)

from nautilus_trader.common.component import LiveClock
from nautilus_trader.cache.cache import Cache
from nautilus_trader.common.component import MessageBus
from nautilus_trader.model.identifiers import ClientId, Venue, TraderId
from nautilus_trader.model.data import DataType, CustomData

from .pa_news_data_client import (
    PANewsDataClient,
    PANewsDataClientConfig,
    PANewsData
)
from simple_news_strategy import (
    SimpleNewsStrategy,
    SimpleNewsStrategyConfig
)


class SimpleNewsRunner:
    """简单的新闻运行器"""
    
    def __init__(self, scraping_interval: int = 180, enable_proxy: bool = True):
        """
        初始化新闻运行器
        
        Args:
            scraping_interval: 爬取间隔（秒），用户可设置
            enable_proxy: 是否启用代理
        """
        self.scraping_interval = scraping_interval
        self.enable_proxy = enable_proxy
        
        # 创建核心组件
        self.clock = LiveClock()
        self.cache = Cache()
        self.msgbus = MessageBus(
            trader_id=TraderId("SimpleNewsTrader"),
            clock=self.clock,
            cache=self.cache
        )
        
        # 组件
        self.news_client = None
        self.strategy = None
        
        print(f"🔧 新闻运行器初始化完成")
        print(f"   爬取间隔: {scraping_interval} 秒")
        print(f"   代理模式: {'启用' if enable_proxy else '禁用'}")
    
    async def setup(self):
        """设置客户端和策略"""
        try:
            # 创建新闻客户端配置
            news_config = PANewsDataClientConfig(
                scraping_interval=self.scraping_interval,  # 用户设置的间隔
                enable_proxy=self.enable_proxy,
                max_news_per_request=3,  # 只获取最新3条
                request_timeout=30
            )
            
            # 创建新闻客户端
            self.news_client = PANewsDataClient(
                loop=asyncio.get_event_loop(),
                client_id=ClientId("PANEWS"),
                venue=Venue("PANEWS"),
                msgbus=self.msgbus,
                cache=self.cache,
                clock=self.clock,
                config=news_config
            )
            
            # 创建策略
            strategy_config = SimpleNewsStrategyConfig(news_client_id="PANEWS")
            self.strategy = SimpleNewsStrategy(config=strategy_config)
            
            # 创建Portfolio facade
            from nautilus_trader.portfolio.portfolio import Portfolio

            portfolio = Portfolio(
                msgbus=self.msgbus,
                cache=self.cache,
                clock=self.clock
            )

            # 注册策略到消息总线（使用完整的register方法）
            self.strategy.register(
                trader_id=TraderId("SimpleNewsTrader"),
                portfolio=portfolio,
                msgbus=self.msgbus,
                cache=self.cache,
                clock=self.clock
            )
            
            print("✅ 客户端和策略设置完成")
            
        except Exception as e:
            print(f"❌ 设置失败: {e}")
            raise
    
    async def start(self):
        """启动新闻爬取和策略"""
        try:
            print("🚀 启动新闻客户端...")
            
            # 连接新闻客户端
            await self.news_client._connect()
            print("✅ 新闻客户端已连接")
            
            # 启动策略
            self.strategy.start()
            print("✅ 策略已启动")
            
            print("\n" + "=" * 60)
            print("📰 开始监听新闻数据...")
            print(f"⏰ 爬取间隔: {self.scraping_interval} 秒")
            print("📝 将显示接收到的最新新闻")
            print("⏹️  按 Ctrl+C 停止")
            print("=" * 60)
            
        except Exception as e:
            print(f"❌ 启动失败: {e}")
            raise
    
    async def stop(self):
        """停止运行器"""
        try:
            print("\n🛑 正在停止...")
            
            # 停止策略
            if self.strategy:
                self.strategy.stop()
                print("✅ 策略已停止")
            
            # 断开新闻客户端
            if self.news_client:
                await self.news_client._disconnect()
                print("✅ 新闻客户端已断开")
            
            print("✅ 运行器已停止")
            
        except Exception as e:
            print(f"❌ 停止时出错: {e}")
    
    async def run(self):
        """运行主循环"""
        try:
            await self.setup()
            await self.start()
            
            # 保持运行直到用户中断
            while True:
                await asyncio.sleep(1)
                
        except KeyboardInterrupt:
            print("\n⏹️ 用户中断")
        except Exception as e:
            print(f"\n❌ 运行出错: {e}")
        finally:
            await self.stop()


async def main():
    """主函数"""
    print("📰 PANews 简单新闻策略")
    print("=" * 40)
    
    # 用户输入配置
    try:
        print("\n⚙️ 配置设置:")
        
        # 爬取间隔设置
        interval_input = input("请输入爬取间隔（秒，默认180）: ").strip()
        if interval_input:
            scraping_interval = int(interval_input)
        else:
            scraping_interval = 180
        
        # 代理设置
        proxy_input = input("是否启用代理？(y/n，默认y): ").strip().lower()
        enable_proxy = proxy_input != 'n'
        
        print(f"\n✅ 配置确认:")
        print(f"   爬取间隔: {scraping_interval} 秒")
        print(f"   代理模式: {'启用' if enable_proxy else '禁用'}")
        
        # 确认启动
        confirm = input("\n是否开始运行？(y/n): ").strip().lower()
        if confirm == 'n':
            print("❌ 用户取消")
            return
        
    except ValueError:
        print("❌ 输入无效，使用默认配置")
        scraping_interval = 180
        enable_proxy = True
    except KeyboardInterrupt:
        print("\n❌ 用户取消")
        return
    
    # 创建并运行
    runner = SimpleNewsRunner(
        scraping_interval=scraping_interval,
        enable_proxy=enable_proxy
    )
    
    await runner.run()


def quick_start(interval: int = 180, proxy: bool = True):
    """快速启动函数"""
    print(f"🚀 快速启动新闻策略")
    print(f"   爬取间隔: {interval} 秒")
    print(f"   代理模式: {'启用' if proxy else '禁用'}")
    
    async def run():
        runner = SimpleNewsRunner(
            scraping_interval=interval,
            enable_proxy=proxy
        )
        await runner.run()
    
    asyncio.run(run())


if __name__ == "__main__":
    try:
        # 检查命令行参数
        if len(sys.argv) > 1:
            if sys.argv[1] == "quick":
                # 快速启动模式
                interval = int(sys.argv[2]) if len(sys.argv) > 2 else 180
                proxy = sys.argv[3].lower() != 'false' if len(sys.argv) > 3 else True
                quick_start(interval, proxy)
            else:
                print("用法:")
                print("  python run_simple_news.py           # 交互模式")
                print("  python run_simple_news.py quick     # 快速启动（默认配置）")
                print("  python run_simple_news.py quick 120 # 快速启动（120秒间隔）")
                print("  python run_simple_news.py quick 120 false # 快速启动（120秒间隔，禁用代理）")
        else:
            # 交互模式
            asyncio.run(main())
            
    except KeyboardInterrupt:
        print("\n⏹️ 程序被中断")
    except Exception as e:
        print(f"\n❌ 程序出错: {e}")
        sys.exit(1)
