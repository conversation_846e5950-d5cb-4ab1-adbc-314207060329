"""
PANewsDataClient与SimpleProxyClient集成测试
验证新闻爬取和代理功能是否正常工作
"""
import asyncio
import logging
import sys
from datetime import datetime

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

try:
    from crawladapter import SimpleProxyClient
    CRAWLADAPTER_AVAILABLE = True
    print("✅ CrawlAdapter 可用")
except ImportError:
    CRAWLADAPTER_AVAILABLE = False
    print("❌ CrawlAdapter 不可用，请安装: pip install git+https://github.com/graceyangfan/CrawlAdapter.git")

from pa_news_data_client import (
    PANewsDataClientConfig,
    PANewsDataClient,
    PANewsData
)
from simple_news_strategy import (
    SimpleNewsStrategy,
    SimpleNewsStrategyConfig
)
from nautilus_trader.model.data import CustomData, DataType


async def test_proxy_client():
    """测试SimpleProxyClient基本功能"""
    print("\n=== 测试SimpleProxyClient ===")
    
    if not CRAWLADAPTER_AVAILABLE:
        print("跳过代理测试（CrawlAdapter不可用）")
        return False
    
    try:
        # 配置自定义代理源（私有Clash节点）
        custom_sources = {
            'clash': [
                # 添加您的私有Clash配置URL
                # 'https://your-private-server.com/clash-config.yml',
                # 'https://another-private-source.com/config.yaml'
            ],
            'v2ray': [
                # 添加您的V2Ray订阅URL
                # 'https://your-v2ray-server.com/subscription'
            ],
            # 可以直接配置节点
            'direct_nodes': [
                # 示例私有节点配置（请替换为实际信息）
                # {
                #     'name': 'my-private-ss',
                #     'type': 'ss',
                #     'server': '*******',
                #     'port': 8388,
                #     'cipher': 'aes-256-gcm',
                #     'password': 'your-password'
                # }
            ]
        }

        # 创建SimpleProxyClient
        proxy_client = SimpleProxyClient(
            config_dir="/workspaces/codespaces-blank/clash_configs",
            clash_binary_path = "/workspaces/codespaces-blank/mihomo_proxy/mihomo",
            custom_sources = custom_sources,
            enable_rules=True
        )
        
        # 启动代理客户端，使用完整的规则集
        print("启动代理客户端...")

        # 完整的代理规则，包含健康检查网站（重要！）
        proxy_rules = [
            # 目标网站
            "*.panewslab.com",
            "panewslab.com",
            "www.panewslab.com",

            # 健康检查网站（这些是关键！）
            "*.gstatic.com",
            "www.gstatic.com",
            "connectivitycheck.gstatic.com",
            "clients3.google.com",
            "clients4.google.com",

            # Google服务（用于健康检查）
            "*.google.com",
            "google.com",

            # IP检测和测试网站
            "*.httpbin.org",
            "httpbin.org",
            "*.ipinfo.io",
            "ipinfo.io",

            # Cloudflare测试
            "*.cloudflare.com",
            "*******",

            # GitHub（用于获取代理配置）
            "*.github.com",
            "*.githubusercontent.com",

            # 其他连通性测试
            "*.detectportal.firefox.com",
            "captive.apple.com"
        ]

        print(f"📋 使用 {len(proxy_rules)} 条代理规则")
        success = await proxy_client.start(rules=proxy_rules)

        if success:
            print("✅ 代理客户端启动成功")

            # 获取详细状态
            status = await proxy_client.get_status()
            print(f"📊 详细代理状态:")
            print(f"   运行状态: {status.get('running', False)}")
            print(f"   总代理数: {status.get('total_proxies', 0)}")
            print(f"   健康代理数: {status.get('healthy_proxies', 0)}")
            print(f"   当前代理: {status.get('current_proxy', 'DIRECT')}")
            print(f"   API可用: {status.get('api_available', False)}")

            # 测试多个URL的代理获取
            test_urls = [
                "https://httpbin.org/ip",
                "https://www.panewslab.com",
                "http://www.gstatic.com/generate_204"
            ]

            for test_url in test_urls:
                try:
                    proxy_url = await proxy_client.get_proxy(test_url)
                    if proxy_url:
                        print(f"✅ {test_url} -> 使用代理: {proxy_url}")
                    else:
                        print(f"⚪ {test_url} -> 直连")
                except Exception as e:
                    print(f"❌ {test_url} -> 获取代理失败: {e}")

            # 停止代理客户端
            await proxy_client.stop()
            print("✅ 代理客户端已停止")
            return True
        else:
            print("❌ 代理客户端启动失败")
            print("💡 可能原因:")
            print("   1. Clash二进制文件路径不正确")
            print("   2. 没有可用的代理节点")
            print("   3. 网络连接问题")
            print("   4. 需要配置私有代理节点")
            return False
            
    except Exception as e:
        print(f"❌ 代理测试失败: {e}")
        return False


def generate_private_proxy_config_example():
    """生成私有代理配置示例"""
    print("\n📝 生成私有代理配置示例")
    print("-" * 40)

    config_example = """# 私有Clash代理配置示例
# 保存为: /workspaces/codespaces-blank/clash_configs/config.yaml

port: 7890
socks-port: 7891
allow-lan: false
mode: rule
log-level: info
external-controller: 127.0.0.1:9090

proxies:
  # 请替换为您的实际代理服务器信息
  - name: "my-private-ss"
    type: ss
    server: "your-server-ip"      # 替换为您的服务器IP
    port: 8388                    # 替换为您的端口
    cipher: aes-256-gcm
    password: "your-password"     # 替换为您的密码

  - name: "my-private-vmess"
    type: vmess
    server: "your-server-ip"      # 替换为您的服务器IP
    port: 443                     # 替换为您的端口
    uuid: "your-uuid"             # 替换为您的UUID
    alterId: 0
    cipher: auto
    tls: true

proxy-groups:
  - name: "PROXY"
    type: select
    proxies:
      - my-private-ss
      - my-private-vmess
      - DIRECT

  - name: "AUTO"
    type: url-test
    proxies:
      - my-private-ss
      - my-private-vmess
    url: 'http://www.gstatic.com/generate_204'
    interval: 300

rules:
  # 目标网站
  - DOMAIN-SUFFIX,panewslab.com,PROXY

  # 健康检查网站（重要！）
  - DOMAIN-SUFFIX,gstatic.com,PROXY
  - DOMAIN,www.gstatic.com,PROXY
  - DOMAIN,connectivitycheck.gstatic.com,PROXY
  - DOMAIN,clients3.google.com,PROXY

  # IP检测网站
  - DOMAIN-SUFFIX,httpbin.org,PROXY
  - DOMAIN-SUFFIX,ipinfo.io,PROXY

  # GitHub
  - DOMAIN-SUFFIX,github.com,PROXY
  - DOMAIN-SUFFIX,githubusercontent.com,PROXY

  # 默认直连
  - MATCH,DIRECT
"""

    import os
    config_dir = "/workspaces/codespaces-blank/clash_configs"
    os.makedirs(config_dir, exist_ok=True)

    config_file = os.path.join(config_dir, "config_example.yaml")
    with open(config_file, "w", encoding="utf-8") as f:
        f.write(config_example)

    print(f"✅ 私有代理配置示例已生成: {config_file}")
    print("\n📋 使用步骤:")
    print("1. 修改配置文件中的服务器信息（IP、端口、密码等）")
    print("2. 重命名为 config.yaml")
    print("3. 重新运行测试")
    print("\n💡 关键点:")
    print("- 确保健康检查网站在代理规则中")
    print("- 使用可靠的私有代理服务器")
    print("- 配置多个备用节点")


async def test_news_client_without_proxy():
    """测试新闻客户端（无代理模式）"""
    print("\n=== 测试新闻客户端（无代理） ===")

    try:
        # 创建配置（禁用代理，用户可设置间隔）
        config = PANewsDataClientConfig(
            enable_proxy=False,
            scraping_interval=60,  # 用户设置的间隔
            max_news_per_request=3  # 只获取最新3条
        )
        
        # 创建必要的组件
        from nautilus_trader.common.component import LiveClock
        from nautilus_trader.cache.cache import Cache
        from nautilus_trader.common.component import MessageBus
        from nautilus_trader.model.identifiers import ClientId, Venue, TraderId
        
        clock = LiveClock()
        cache = Cache()
        msgbus = MessageBus(
            trader_id=TraderId("TEST-001"),
            clock=clock
        )
        
        # 创建新闻客户端
        client = PANewsDataClient(
            loop=asyncio.get_event_loop(),
            client_id=ClientId("PANEWS"),
            venue=Venue("PANEWS"),
            msgbus=msgbus,
            cache=cache,
            clock=clock,
            config=config
        )
        
        print("连接新闻客户端...")
        await client._connect()
        print("✅ 新闻客户端连接成功")
        
        # 手动触发爬取
        print("手动爬取新闻...")
        news_items = await client.manual_scrape()
        
        print(f"✅ 爬取成功，获得 {len(news_items)} 条新闻")
        
        # 显示前3条新闻
        for i, news in enumerate(news_items[:3], 1):
            print(f"\n新闻 {i}:")
            print(f"  标题: {news.title}")
            print(f"  时间: {news.publish_time}")
            print(f"  符号: {news.symbols}")
            print(f"  加密货币相关: {news.is_crypto_related()}")
        
        # 断开连接
        await client._disconnect()
        print("✅ 新闻客户端已断开")
        return True
        
    except Exception as e:
        print(f"❌ 新闻客户端测试失败: {e}")
        return False


async def test_news_client_with_proxy():
    """测试新闻客户端（代理模式）"""
    print("\n=== 测试新闻客户端（代理模式） ===")
    
    if not CRAWLADAPTER_AVAILABLE:
        print("跳过代理模式测试（CrawlAdapter不可用）")
        return False
    
    try:
        # 创建配置（启用代理）
        config = PANewsDataClientConfig(
            enable_proxy=True,
            scraping_interval=60,
            max_news_per_request=5,
            proxy_rules=[
                "*.panewslab.com",
                "*.httpbin.org"
            ]
        )
        
        # 创建必要的组件
        from nautilus_trader.common.component import LiveClock
        from nautilus_trader.cache.cache import Cache
        from nautilus_trader.common.component import MessageBus
        from nautilus_trader.model.identifiers import ClientId, Venue, TraderId
        
        clock = LiveClock()
        cache = Cache()
        msgbus = MessageBus(
            trader_id=TraderId("TEST-002"),
            clock=clock
        )
        
        # 创建新闻客户端
        client = PANewsDataClient(
            loop=asyncio.get_event_loop(),
            client_id=ClientId("PANEWS"),
            venue=Venue("PANEWS"),
            msgbus=msgbus,
            cache=cache,
            clock=clock,
            config=config
        )
        
        print("连接新闻客户端（代理模式）...")
        await client._connect()
        print("✅ 新闻客户端连接成功")
        
        # 获取代理状态
        proxy_status = await client.get_proxy_status()
        print(f"代理状态: {proxy_status}")
        
        # 手动触发爬取
        print("手动爬取新闻（使用代理）...")
        news_items = await client.manual_scrape()
        
        print(f"✅ 爬取成功，获得 {len(news_items)} 条新闻")
        
        # 显示前2条新闻
        for i, news in enumerate(news_items[:2], 1):
            print(f"\n新闻 {i}:")
            print(f"  标题: {news.title[:50]}...")
            print(f"  时间: {news.publish_time}")
            print(f"  符号: {news.symbols}")
        
        # 测试代理切换
        print("\n测试代理切换...")
        switch_result = await client.switch_proxy()
        print(f"代理切换结果: {switch_result}")
        
        # 断开连接
        await client._disconnect()
        print("✅ 新闻客户端已断开")
        return True
        
    except Exception as e:
        print(f"❌ 代理模式测试失败: {e}")
        return False


async def test_data_types():
    """测试数据类型"""
    print("\n=== 测试数据类型 ===")
    
    try:
        # 创建测试新闻数据
        news = PANewsData(
            title="比特币价格突破50000美元",
            content="比特币(BTC)今日突破50000美元大关，创下新高。以太坊(ETH)也跟随上涨。",
            url="https://www.panewslab.com/zh/articledetails/test123.html",
            publish_time="2024-01-15 10:30:00",
            symbols="BTC,ETH",
            category="market",
            source="PANews",
            news_id="test123",
            ts_event=1642234200000000000,
            ts_init=1642234200000000000
        )
        
        print("✅ PANewsData 创建成功")
        print(f"标题: {news.title}")
        print(f"符号列表: {news.get_symbols_list()}")
        print(f"加密货币相关: {news.is_crypto_related()}")
        
        return True

    except Exception as e:
        print(f"❌ 数据类型测试失败: {e}")
        return False


async def test_simple_strategy():
    """测试简化策略"""
    print("\n=== 测试简化新闻策略 ===")

    try:
        # 创建策略配置
        strategy_config = SimpleNewsStrategyConfig(news_client_id="PANEWS")

        # 创建策略
        strategy = SimpleNewsStrategy(config=strategy_config)

        # 创建必要的组件
        from nautilus_trader.common.component import LiveClock
        from nautilus_trader.cache.cache import Cache
        from nautilus_trader.common.component import MessageBus
        from nautilus_trader.model.identifiers import TraderId

        clock = LiveClock()
        cache = Cache()
        msgbus = MessageBus(
            trader_id=TraderId("TEST-003"),
            clock=clock
        )

        # 创建Portfolio facade
        from nautilus_trader.portfolio.portfolio import Portfolio

        portfolio = Portfolio(
            msgbus=msgbus,
            cache=cache,
            clock=clock
        )

        # 注册策略（使用完整的register方法）
        strategy.register(
            trader_id=TraderId("TEST-003"),
            portfolio=portfolio,
            msgbus=msgbus,
            cache=cache,
            clock=clock
        )

        print("✅ 策略创建成功")

        # 启动策略
        strategy.start()
        print("✅ 策略启动成功")

        # 创建测试新闻数据
        test_news = PANewsData(
            title="测试新闻：比特币价格突破新高",
            content="这是一条测试新闻，用于验证策略功能。比特币(BTC)价格创新高。",
            url="https://test.com/news/1",
            publish_time="2024-01-15 10:30:00",
            symbols="BTC",
            category="market",
            source="PANews",
            news_id="test001",
            ts_event=clock.timestamp_ns(),
            ts_init=clock.timestamp_ns()
        )

        # 创建CustomData包装
        custom_data = CustomData(
            data_type=DataType(PANewsData, metadata={"source": "PANews"}),
            data=test_news
        )

        print("📰 发送测试新闻到策略...")

        # 发送数据到策略
        strategy.on_data(custom_data)

        print("✅ 策略测试完成")

        # 停止策略
        strategy.stop()
        print("✅ 策略已停止")

        return True

    except Exception as e:
        print(f"❌ 策略测试失败: {e}")
        return False


async def main():
    """主测试函数"""
    print("PANewsDataClient 集成测试")
    print("=" * 50)
    
    test_results = []
    
    # 测试数据类型
    result = await test_data_types()
    test_results.append(("数据类型", result))
    
    # 测试代理客户端
    result = await test_proxy_client()
    test_results.append(("代理客户端", result))
    
    # 测试新闻客户端（无代理）
    result = await test_news_client_without_proxy()
    test_results.append(("新闻客户端（无代理）", result))
    
    # 测试新闻客户端（代理模式）
    result = await test_news_client_with_proxy()
    test_results.append(("新闻客户端（代理模式）", result))

    # 测试简化策略
    result = await test_simple_strategy()
    test_results.append(("简化新闻策略", result))

    # 如果代理测试失败，生成配置示例
    proxy_tests_failed = not test_results[1][1]  # 代理客户端测试结果
    if proxy_tests_failed:
        generate_private_proxy_config_example()
    
    # 显示测试结果
    print("\n" + "=" * 50)
    print("测试结果汇总:")
    print("=" * 50)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！")
        return 0
    else:
        print("⚠️ 部分测试失败")
        return 1


if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n测试运行失败: {e}")
        sys.exit(1)
