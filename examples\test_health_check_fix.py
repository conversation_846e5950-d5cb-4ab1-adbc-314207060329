"""
测试健康检查修复效果
验证代理健康检查是否能正确识别可用代理
"""
import asyncio
import logging
from pathlib import Path

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

try:
    from crawladapter import SimpleProxyClient
    CRAWLADAPTER_AVAILABLE = True
    print("✅ CrawlAdapter 可用")
except ImportError:
    CRAWLADAPTER_AVAILABLE = False
    print("❌ CrawlAdapter 不可用")
    exit(1)


async def test_health_check_fix():
    """测试健康检查修复效果"""
    print("\n=== 测试健康检查修复效果 ===")
    
    try:
        # 配置代理源
        custom_sources = {
            'clash': [
                'https://7izza.no-mad-world.club/link/iHil1Ll4I1XzfGDW?clash=3&extend=1',
            ],
            'v2ray': []
        }

        # 获取项目根目录路径
        project_root = Path(__file__).parent.parent
        config_dir = project_root / 'clash_configs'
        clash_binary_path = project_root / 'mihomo_proxy' / 'mihomo'
        
        # 确保目录存在
        config_dir.mkdir(exist_ok=True)

        # 创建SimpleProxyClient
        proxy_client = SimpleProxyClient(
            config_dir=str(config_dir),
            clash_binary_path=str(clash_binary_path),
            custom_sources=custom_sources,
            enable_rules=True
        )
        
        # 启动代理客户端
        print("启动代理客户端...")
        
        proxy_rules = [
            "*.panewslab.com",
            "panewslab.com",
            "*.httpbin.org",
            "httpbin.org",
            "*.ipify.org",
            "*.gstatic.com"
        ]

        print(f"📋 使用 {len(proxy_rules)} 条代理规则")
        success = await proxy_client.start(rules=proxy_rules)

        if success:
            print("✅ 代理客户端启动成功")

            # 获取详细状态
            status = await proxy_client.get_status()
            print(f"📊 修复后的代理状态:")
            print(f"   运行状态: {status.get('running', False)}")
            print(f"   总代理数: {status.get('total_proxies', 0)}")
            print(f"   健康代理数: {status.get('healthy_proxies', 0)}")
            print(f"   当前代理: {status.get('current_proxy', 'DIRECT')}")
            print(f"   API可用: {status.get('api_available', False)}")
            
            # 计算健康率
            total = status.get('total_proxies', 0)
            healthy = status.get('healthy_proxies', 0)
            if total > 0:
                health_rate = (healthy / total) * 100
                print(f"   健康率: {health_rate:.1f}%")
                
                if health_rate > 10:
                    print("🎉 健康检查修复成功！现在能正确识别可用代理了")
                elif health_rate > 0:
                    print("✅ 健康检查有改善，部分代理被正确识别")
                else:
                    print("❌ 健康检查仍有问题，需要进一步调试")
            
            # 测试代理功能
            test_urls = [
                "https://httpbin.org/ip",
                "https://api.ipify.org",
                "https://www.panewslab.com"
            ]

            print("\n🔍 测试代理功能:")
            for test_url in test_urls:
                try:
                    proxy_url = await proxy_client.get_proxy(test_url)
                    if proxy_url:
                        print(f"✅ {test_url} -> 使用代理: {proxy_url}")
                    else:
                        print(f"⚪ {test_url} -> 直连")
                except Exception as e:
                    print(f"❌ {test_url} -> 获取代理失败: {e}")

            # 停止代理客户端
            await proxy_client.stop()
            print("✅ 代理客户端已停止")
            return True
        else:
            print("❌ 代理客户端启动失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def main():
    """主函数"""
    print("健康检查修复效果测试")
    print("=" * 50)
    
    success = await test_health_check_fix()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 测试完成！")
    else:
        print("❌ 测试失败！")


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n测试被用户中断")
    except Exception as e:
        print(f"\n测试运行失败: {e}")
