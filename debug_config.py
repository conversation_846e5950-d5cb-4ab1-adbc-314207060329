"""
调试配置生成问题
"""
import sys
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from crawladapter.managers import ConfigurationManager
from crawladapter.fetchers import NodeFetcher

async def debug_config_generation():
    """调试配置生成过程"""
    try:
        print("=== 调试配置生成过程 ===")
        
        # 1. 测试节点获取
        print("1. 测试节点获取...")
        custom_sources = {
            'clash': [
                'https://7izza.no-mad-world.club/link/iHil1Ll4I1XzfGDW?clash=3&extend=1',
            ],
            'v2ray': []
        }
        
        node_fetcher = NodeFetcher(custom_sources=custom_sources)
        nodes = await node_fetcher.fetch_nodes('all')
        print(f"✅ 获取到 {len(nodes)} 个节点")
        
        if not nodes:
            print("❌ 没有获取到节点，无法继续测试")
            return
        
        # 2. 测试配置生成
        print("2. 测试配置生成...")
        config_manager = ConfigurationManager("./test_configs")
        
        # 转换节点格式
        proxy_dicts = [node.to_dict() for node in nodes[:5]]  # 只用前5个节点测试
        print(f"✅ 转换了 {len(proxy_dicts)} 个节点")
        
        # 生成配置（不包含健康检查规则）
        print("3. 生成普通配置...")
        try:
            normal_config = config_manager.generate_clash_config(
                proxy_dicts, 
                'scraping', 
                include_health_check_rules=False
            )
            print("✅ 普通配置生成成功")
            print(f"   代理数量: {len(normal_config.get('proxies', []))}")
            print(f"   规则数量: {len(normal_config.get('rules', []))}")
        except Exception as e:
            print(f"❌ 普通配置生成失败: {e}")
            import traceback
            traceback.print_exc()
            return
        
        # 生成配置（包含健康检查规则）
        print("4. 生成健康检查配置...")
        try:
            health_config = config_manager.generate_clash_config(
                proxy_dicts, 
                'scraping', 
                include_health_check_rules=True
            )
            print("✅ 健康检查配置生成成功")
            print(f"   代理数量: {len(health_config.get('proxies', []))}")
            print(f"   规则数量: {len(health_config.get('rules', []))}")
            
            # 显示前几条规则
            rules = health_config.get('rules', [])
            print("   前10条规则:")
            for i, rule in enumerate(rules[:10]):
                print(f"     {i+1}. {rule}")
                
        except Exception as e:
            print(f"❌ 健康检查配置生成失败: {e}")
            import traceback
            traceback.print_exc()
            return
        
        # 5. 测试配置保存
        print("5. 测试配置保存...")
        try:
            config_path = config_manager.save_configuration(health_config)
            print(f"✅ 配置保存成功: {config_path}")
            
            # 检查文件是否存在
            if Path(config_path).exists():
                file_size = Path(config_path).stat().st_size
                print(f"   文件大小: {file_size} 字节")
            else:
                print("❌ 配置文件不存在")
                
        except Exception as e:
            print(f"❌ 配置保存失败: {e}")
            import traceback
            traceback.print_exc()
            return
            
        print("🎉 配置生成调试完成")
        
    except Exception as e:
        print(f"❌ 调试过程失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    import asyncio
    asyncio.run(debug_config_generation())
